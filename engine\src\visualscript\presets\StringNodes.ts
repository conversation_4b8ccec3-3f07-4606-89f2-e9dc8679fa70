/**
 * 视觉脚本字符串操作节点
 * 提供字符串处理相关功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 字符串连接节点
 * 将多个字符串连接成一个字符串
 */
export class StringConcatNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string1',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '第一个字符串',
      defaultValue: ''
    });

    this.addInput({
      name: 'string2',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '第二个字符串',
      defaultValue: ''
    });

    this.addInput({
      name: 'separator',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分隔符',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '连接后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string1 = this.getInputValue('string1') as string || '';
    const string2 = this.getInputValue('string2') as string || '';
    const separator = this.getInputValue('separator') as string || '';

    // 连接字符串
    const result = string1 + separator + string2;

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 子字符串节点
 * 从字符串中提取子字符串
 */
export class SubstringNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '原字符串',
      defaultValue: ''
    });

    this.addInput({
      name: 'start',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '开始位置',
      defaultValue: 0
    });

    this.addInput({
      name: 'length',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '长度（可选）',
      defaultValue: -1
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '子字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = this.getInputValue('string') as string || '';
    const start = this.getInputValue('start') as number || 0;
    const length = this.getInputValue('length') as number || -1;

    // 提取子字符串
    let result: string;
    if (length === -1) {
      result = string.substring(start);
    } else {
      result = string.substring(start, start + length);
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 字符串替换节点
 * 替换字符串中的内容
 */
export class StringReplaceNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '原字符串',
      defaultValue: ''
    });

    this.addInput({
      name: 'searchValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要替换的内容',
      defaultValue: ''
    });

    this.addInput({
      name: 'replaceValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '替换为',
      defaultValue: ''
    });

    this.addInput({
      name: 'replaceAll',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '替换所有',
      defaultValue: false
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '替换后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = this.getInputValue('string') as string || '';
    const searchValue = this.getInputValue('searchValue') as string || '';
    const replaceValue = this.getInputValue('replaceValue') as string || '';
    const replaceAll = this.getInputValue('replaceAll') as boolean || false;

    // 替换字符串
    let result: string;
    if (replaceAll) {
      result = string.replaceAll(searchValue, replaceValue);
    } else {
      result = string.replace(searchValue, replaceValue);
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 字符串长度节点
 * 获取字符串的长度
 */
export class StringLengthNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '字符串长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = this.getInputValue('string') as string || '';

    // 获取字符串长度
    const length = string.length;

    // 设置输出值
    this.setOutputValue('length', length);

    // 触发输出流程
    this.triggerFlow('flow');

    return length;
  }
}

/**
 * 转大写节点
 * 将字符串转换为大写
 */
export class StringToUpperCaseNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '大写字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = this.getInputValue('string') as string || '';

    // 转换为大写
    const result = string.toUpperCase();

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 转小写节点
 * 将字符串转换为小写
 */
export class StringToLowerCaseNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '小写字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = this.getInputValue('string') as string || '';

    // 转换为小写
    const result = string.toLowerCase();

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 去除空格节点
 * 去除字符串首尾的空格
 */
export class StringTrimNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '去除空格后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = this.getInputValue('string') as string || '';

    // 去除首尾空格
    const result = string.trim();

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 注册字符串操作节点
 * @param registry 节点注册表
 */
export function registerStringNodes(registry: NodeRegistry): void {
  // 注册字符串连接节点
  registry.registerNodeType({
    type: 'string/concat',
    category: NodeCategory.STRING,
    constructor: StringConcatNode,
    label: '字符串连接',
    description: '将多个字符串连接成一个字符串',
    icon: 'concat',
    color: '#9C27B0',
    tags: ['string', 'concat', 'join']
  });

  // 注册子字符串节点
  registry.registerNodeType({
    type: 'string/substring',
    category: NodeCategory.STRING,
    constructor: SubstringNode,
    label: '子字符串',
    description: '从字符串中提取子字符串',
    icon: 'substring',
    color: '#9C27B0',
    tags: ['string', 'substring', 'extract']
  });

  // 注册字符串替换节点
  registry.registerNodeType({
    type: 'string/replace',
    category: NodeCategory.STRING,
    constructor: StringReplaceNode,
    label: '字符串替换',
    description: '替换字符串中的内容',
    icon: 'replace',
    color: '#9C27B0',
    tags: ['string', 'replace', 'substitute']
  });

  // 注册字符串分割节点
  registry.registerNodeType({
    type: 'string/split',
    category: NodeCategory.STRING,
    constructor: StringSplitNode,
    label: '字符串分割',
    description: '将字符串分割成数组',
    icon: 'split',
    color: '#9C27B0',
    tags: ['string', 'split', 'array']
  });

  // 注册字符串长度节点
  registry.registerNodeType({
    type: 'string/length',
    category: NodeCategory.STRING,
    constructor: StringLengthNode,
    label: '字符串长度',
    description: '获取字符串的长度',
    icon: 'length',
    color: '#9C27B0',
    tags: ['string', 'length', 'size']
  });

  // 注册转大写节点
  registry.registerNodeType({
    type: 'string/toUpperCase',
    category: NodeCategory.STRING,
    constructor: StringToUpperCaseNode,
    label: '转大写',
    description: '将字符串转换为大写',
    icon: 'uppercase',
    color: '#9C27B0',
    tags: ['string', 'uppercase', 'case']
  });

  // 注册转小写节点
  registry.registerNodeType({
    type: 'string/toLowerCase',
    category: NodeCategory.STRING,
    constructor: StringToLowerCaseNode,
    label: '转小写',
    description: '将字符串转换为小写',
    icon: 'lowercase',
    color: '#9C27B0',
    tags: ['string', 'lowercase', 'case']
  });

  // 注册去除空格节点
  registry.registerNodeType({
    type: 'string/trim',
    category: NodeCategory.STRING,
    constructor: StringTrimNode,
    label: '去除空格',
    description: '去除字符串首尾的空格',
    icon: 'trim',
    color: '#9C27B0',
    tags: ['string', 'trim', 'whitespace']
  });
}

/**
 * 字符串分割节点
 * 将字符串分割成数组
 */
export class StringSplitNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.STRING;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加字符串输入插槽
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要分割的字符串',
      defaultValue: ''
    });

    this.addInput({
      name: 'separator',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '分隔符',
      defaultValue: ','
    });

    this.addInput({
      name: 'limit',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '限制数量（可选）',
      defaultValue: -1
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '分割后的数组'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = this.getInputValue('string') as string || '';
    const separator = this.getInputValue('separator') as string || ',';
    const limit = this.getInputValue('limit') as number || -1;

    // 分割字符串
    let result: string[];
    if (limit === -1) {
      result = string.split(separator);
    } else {
      result = string.split(separator, limit);
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}
