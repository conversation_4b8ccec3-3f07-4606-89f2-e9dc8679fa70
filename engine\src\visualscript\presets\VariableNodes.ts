/**
 * 视觉脚本变量操作节点
 * 提供变量处理相关功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

/**
 * 获取变量节点
 * 获取变量的值
 */
export class VariableGetNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.VARIABLE;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加变量名输入插槽
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名',
      defaultValue: ''
    });

    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '默认值（可选）'
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '变量值'
    });

    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '变量是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string || '';
    const defaultValue = this.getInputValue('defaultValue');

    // 获取变量值
    const exists = this.context.hasVariable(name);
    const value = exists ? this.context.getVariable(name) : defaultValue;

    // 设置输出值
    this.setOutputValue('value', value);
    this.setOutputValue('exists', exists);

    // 触发输出流程
    this.triggerFlow('flow');

    return value;
  }
}

/**
 * 设置变量节点
 * 设置变量的值
 */
export class VariableSetNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.VARIABLE;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加变量名输入插槽
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名',
      defaultValue: ''
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '变量值'
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'previousValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '之前的值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string || '';
    const value = this.getInputValue('value');

    // 获取之前的值
    const previousValue = this.context.hasVariable(name) ? this.context.getVariable(name) : undefined;

    // 设置变量值
    if (name) {
      this.context.setVariable(name, value);
    }

    // 设置输出值
    this.setOutputValue('previousValue', previousValue);

    // 触发输出流程
    this.triggerFlow('flow');

    return value;
  }
}

/**
 * 变量递增节点
 * 递增变量的值
 */
export class VariableIncrementNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.VARIABLE;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加变量名输入插槽
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名',
      defaultValue: ''
    });

    this.addInput({
      name: 'step',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '步长',
      defaultValue: 1
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '递增后的值'
    });

    this.addOutput({
      name: 'previousValue',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '递增前的值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string || '';
    const step = this.getInputValue('step') as number || 1;

    // 获取之前的值
    const previousValue = this.context.hasVariable(name) ? Number(this.context.getVariable(name)) || 0 : 0;

    // 递增变量值
    const value = previousValue + step;
    if (name) {
      this.context.setVariable(name, value);
    }

    // 设置输出值
    this.setOutputValue('value', value);
    this.setOutputValue('previousValue', previousValue);

    // 触发输出流程
    this.triggerFlow('flow');

    return value;
  }
}

/**
 * 变量递减节点
 * 递减变量的值
 */
export class VariableDecrementNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.VARIABLE;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加变量名输入插槽
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名',
      defaultValue: ''
    });

    this.addInput({
      name: 'step',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '步长',
      defaultValue: 1
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '递减后的值'
    });

    this.addOutput({
      name: 'previousValue',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '递减前的值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string || '';
    const step = this.getInputValue('step') as number || 1;

    // 获取之前的值
    const previousValue = this.context.hasVariable(name) ? Number(this.context.getVariable(name)) || 0 : 0;

    // 递减变量值
    const value = previousValue - step;
    if (name) {
      this.context.setVariable(name, value);
    }

    // 设置输出值
    this.setOutputValue('value', value);
    this.setOutputValue('previousValue', previousValue);

    // 触发输出流程
    this.triggerFlow('flow');

    return value;
  }
}

/**
 * 变量存在节点
 * 检查变量是否存在
 */
export class VariableExistsNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.VARIABLE;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加变量名输入插槽
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '变量是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string || '';

    // 检查变量是否存在
    const exists = this.context.hasVariable(name);

    // 设置输出值
    this.setOutputValue('exists', exists);

    // 触发输出流程
    this.triggerFlow('flow');

    return exists;
  }
}

/**
 * 删除变量节点
 * 删除变量
 */
export class VariableDeleteNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.VARIABLE;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加变量名输入插槽
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功删除'
    });

    this.addOutput({
      name: 'previousValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '删除前的值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string || '';

    // 获取之前的值
    const exists = this.context.hasVariable(name);
    const previousValue = exists ? this.context.getVariable(name) : undefined;

    // 删除变量
    const success = name && exists;
    if (success) {
      this.context.deleteVariable(name);
    }

    // 设置输出值
    this.setOutputValue('success', success);
    this.setOutputValue('previousValue', previousValue);

    // 触发输出流程
    this.triggerFlow('flow');

    return success;
  }
}

/**
 * 变量类型节点
 * 获取变量的类型
 */
export class VariableTypeNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.VARIABLE;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加变量名输入插槽
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '变量名',
      defaultValue: ''
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'type',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '变量类型'
    });

    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '变量是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = this.getInputValue('name') as string || '';

    // 获取变量类型
    const exists = this.context.hasVariable(name);
    const value = exists ? this.context.getVariable(name) : undefined;
    const type = exists ? typeof value : 'undefined';

    // 设置输出值
    this.setOutputValue('type', type);
    this.setOutputValue('exists', exists);

    // 触发输出流程
    this.triggerFlow('flow');

    return type;
  }
}

/**
 * 注册变量操作节点
 * @param registry 节点注册表
 */
export function registerVariableNodes(registry: NodeRegistry): void {
  // 注册获取变量节点
  registry.registerNodeType({
    type: 'variable/get',
    category: NodeCategory.VARIABLE,
    constructor: VariableGetNode,
    label: '获取变量',
    description: '获取变量的值',
    icon: 'get',
    color: '#607D8B',
    tags: ['variable', 'get', 'value']
  });

  // 注册设置变量节点
  registry.registerNodeType({
    type: 'variable/set',
    category: NodeCategory.VARIABLE,
    constructor: VariableSetNode,
    label: '设置变量',
    description: '设置变量的值',
    icon: 'set',
    color: '#607D8B',
    tags: ['variable', 'set', 'value']
  });

  // 注册变量递增节点
  registry.registerNodeType({
    type: 'variable/increment',
    category: NodeCategory.VARIABLE,
    constructor: VariableIncrementNode,
    label: '变量递增',
    description: '递增变量的值',
    icon: 'increment',
    color: '#607D8B',
    tags: ['variable', 'increment', 'add']
  });

  // 注册变量递减节点
  registry.registerNodeType({
    type: 'variable/decrement',
    category: NodeCategory.VARIABLE,
    constructor: VariableDecrementNode,
    label: '变量递减',
    description: '递减变量的值',
    icon: 'decrement',
    color: '#607D8B',
    tags: ['variable', 'decrement', 'subtract']
  });

  // 注册变量存在节点
  registry.registerNodeType({
    type: 'variable/exists',
    category: NodeCategory.VARIABLE,
    constructor: VariableExistsNode,
    label: '变量存在',
    description: '检查变量是否存在',
    icon: 'exists',
    color: '#607D8B',
    tags: ['variable', 'exists', 'check']
  });

  // 注册删除变量节点
  registry.registerNodeType({
    type: 'variable/delete',
    category: NodeCategory.VARIABLE,
    constructor: VariableDeleteNode,
    label: '删除变量',
    description: '删除变量',
    icon: 'delete',
    color: '#607D8B',
    tags: ['variable', 'delete', 'remove']
  });

  // 注册变量类型节点
  registry.registerNodeType({
    type: 'variable/type',
    category: NodeCategory.VARIABLE,
    constructor: VariableTypeNode,
    label: '变量类型',
    description: '获取变量的类型',
    icon: 'type',
    color: '#607D8B',
    tags: ['variable', 'type', 'typeof']
  });
}
