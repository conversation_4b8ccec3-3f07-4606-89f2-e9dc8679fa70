/**
 * 视觉脚本数组操作节点
 * 提供数组处理相关功能
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 数组添加节点
 * 向数组末尾添加元素
 */
export class ArrayPushNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要添加的元素'
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '修改后的数组'
    });

    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '新的数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = [...(this.getInputValue('array') as any[] || [])];
    const element = this.getInputValue('element');

    // 添加元素
    const length = array.push(element);

    // 设置输出值
    this.setOutputValue('result', array);
    this.setOutputValue('length', length);

    // 触发输出流程
    this.triggerFlow('flow');

    return array;
  }
}

/**
 * 数组弹出节点
 * 从数组末尾移除并返回元素
 */
export class ArrayPopNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '修改后的数组'
    });

    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '弹出的元素'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = [...(this.getInputValue('array') as any[] || [])];

    // 弹出元素
    const element = array.pop();

    // 设置输出值
    this.setOutputValue('result', array);
    this.setOutputValue('element', element);

    // 触发输出流程
    this.triggerFlow('flow');

    return array;
  }
}

/**
 * 数组长度节点
 * 获取数组的长度
 */
export class ArrayLengthNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = this.getInputValue('array') as any[] || [];

    // 获取数组长度
    const length = array.length;

    // 设置输出值
    this.setOutputValue('length', length);

    // 触发输出流程
    this.triggerFlow('flow');

    return length;
  }
}

/**
 * 获取数组元素节点
 * 根据索引获取数组元素
 */
export class ArrayGetNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    this.addInput({
      name: 'index',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '索引',
      defaultValue: 0
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '获取的元素'
    });

    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '元素是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = this.getInputValue('array') as any[] || [];
    const index = this.getInputValue('index') as number || 0;

    // 获取元素
    const exists = index >= 0 && index < array.length;
    const element = exists ? array[index] : undefined;

    // 设置输出值
    this.setOutputValue('element', element);
    this.setOutputValue('exists', exists);

    // 触发输出流程
    this.triggerFlow('flow');

    return element;
  }
}

/**
 * 设置数组元素节点
 * 根据索引设置数组元素
 */
export class ArraySetNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    this.addInput({
      name: 'index',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要设置的元素'
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '修改后的数组'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否设置成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = [...(this.getInputValue('array') as any[] || [])];
    const index = this.getInputValue('index') as number || 0;
    const element = this.getInputValue('element');

    // 设置元素
    const success = index >= 0;
    if (success) {
      array[index] = element;
    }

    // 设置输出值
    this.setOutputValue('result', array);
    this.setOutputValue('success', success);

    // 触发输出流程
    this.triggerFlow('flow');

    return array;
  }
}

/**
 * 查找数组索引节点
 * 查找元素在数组中的索引
 */
export class ArrayIndexOfNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要查找的元素'
    });

    this.addInput({
      name: 'fromIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '开始查找的索引',
      defaultValue: 0
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'index',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '元素索引（-1表示未找到）'
    });

    this.addOutput({
      name: 'found',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否找到元素'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = this.getInputValue('array') as any[] || [];
    const element = this.getInputValue('element');
    const fromIndex = this.getInputValue('fromIndex') as number || 0;

    // 查找元素索引
    const index = array.indexOf(element, fromIndex);
    const found = index !== -1;

    // 设置输出值
    this.setOutputValue('index', index);
    this.setOutputValue('found', found);

    // 触发输出流程
    this.triggerFlow('flow');

    return index;
  }
}

/**
 * 数组切片节点
 * 提取数组的一部分
 */
export class ArraySliceNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    this.addInput({
      name: 'start',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '开始索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'end',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '结束索引（可选）',
      defaultValue: -1
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '切片后的数组'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = this.getInputValue('array') as any[] || [];
    const start = this.getInputValue('start') as number || 0;
    const end = this.getInputValue('end') as number || -1;

    // 切片数组
    let result: any[];
    if (end === -1) {
      result = array.slice(start);
    } else {
      result = array.slice(start, end);
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 数组排序节点
 * 对数组进行排序
 */
export class ArraySortNode extends FunctionNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.ARRAY;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加流程输入插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加数组输入插槽
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组',
      defaultValue: []
    });

    this.addInput({
      name: 'ascending',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '升序排列',
      defaultValue: true
    });

    this.addInput({
      name: 'numeric',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '数值排序',
      defaultValue: false
    });

    // 添加流程输出插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    // 添加结果输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '排序后的数组'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const array = [...(this.getInputValue('array') as any[] || [])];
    const ascending = this.getInputValue('ascending') as boolean ?? true;
    const numeric = this.getInputValue('numeric') as boolean ?? false;

    // 排序数组
    let result: any[];
    if (numeric) {
      result = array.sort((a, b) => {
        const numA = Number(a);
        const numB = Number(b);
        return ascending ? numA - numB : numB - numA;
      });
    } else {
      result = array.sort((a, b) => {
        const strA = String(a);
        const strB = String(b);
        if (ascending) {
          return strA.localeCompare(strB);
        } else {
          return strB.localeCompare(strA);
        }
      });
    }

    // 设置输出值
    this.setOutputValue('result', result);

    // 触发输出流程
    this.triggerFlow('flow');

    return result;
  }
}

/**
 * 注册数组操作节点
 * @param registry 节点注册表
 */
export function registerArrayNodes(registry: NodeRegistry): void {
  // 注册数组添加节点
  registry.registerNodeType({
    type: 'array/push',
    category: NodeCategory.ARRAY,
    constructor: ArrayPushNode,
    label: '数组添加',
    description: '向数组末尾添加元素',
    icon: 'push',
    color: '#E91E63',
    tags: ['array', 'push', 'add']
  });

  // 注册数组弹出节点
  registry.registerNodeType({
    type: 'array/pop',
    category: NodeCategory.ARRAY,
    constructor: ArrayPopNode,
    label: '数组弹出',
    description: '从数组末尾移除并返回元素',
    icon: 'pop',
    color: '#E91E63',
    tags: ['array', 'pop', 'remove']
  });

  // 注册数组长度节点
  registry.registerNodeType({
    type: 'array/length',
    category: NodeCategory.ARRAY,
    constructor: ArrayLengthNode,
    label: '数组长度',
    description: '获取数组的长度',
    icon: 'length',
    color: '#E91E63',
    tags: ['array', 'length', 'size']
  });

  // 注册获取数组元素节点
  registry.registerNodeType({
    type: 'array/get',
    category: NodeCategory.ARRAY,
    constructor: ArrayGetNode,
    label: '获取元素',
    description: '根据索引获取数组元素',
    icon: 'get',
    color: '#E91E63',
    tags: ['array', 'get', 'element']
  });

  // 注册设置数组元素节点
  registry.registerNodeType({
    type: 'array/set',
    category: NodeCategory.ARRAY,
    constructor: ArraySetNode,
    label: '设置元素',
    description: '根据索引设置数组元素',
    icon: 'set',
    color: '#E91E63',
    tags: ['array', 'set', 'element']
  });

  // 注册查找数组索引节点
  registry.registerNodeType({
    type: 'array/indexOf',
    category: NodeCategory.ARRAY,
    constructor: ArrayIndexOfNode,
    label: '查找索引',
    description: '查找元素在数组中的索引',
    icon: 'search',
    color: '#E91E63',
    tags: ['array', 'indexOf', 'search']
  });

  // 注册数组切片节点
  registry.registerNodeType({
    type: 'array/slice',
    category: NodeCategory.ARRAY,
    constructor: ArraySliceNode,
    label: '数组切片',
    description: '提取数组的一部分',
    icon: 'slice',
    color: '#E91E63',
    tags: ['array', 'slice', 'extract']
  });

  // 注册数组排序节点
  registry.registerNodeType({
    type: 'array/sort',
    category: NodeCategory.ARRAY,
    constructor: ArraySortNode,
    label: '数组排序',
    description: '对数组进行排序',
    icon: 'sort',
    color: '#E91E63',
    tags: ['array', 'sort', 'order']
  });
}
